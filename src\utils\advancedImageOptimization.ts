/**
 * Advanced Image Optimization Utilities
 * Reduces image waste by implementing smart sizing and format selection
 */

export interface ImageOptimizationConfig {
  quality: number;
  format: 'webp' | 'avif' | 'auto';
  enablePlaceholder: boolean;
  enableLazyLoading: boolean;
  enableResponsive: boolean;
}

export interface ResponsiveImageSizes {
  mobile: { width: number; height: number };
  tablet: { width: number; height: number };
  desktop: { width: number; height: number };
  large: { width: number; height: number };
}

export interface OptimizedImageResult {
  src: string;
  srcset: string;
  sizes: string;
  placeholder?: string;
  width: number;
  height: number;
}

/**
 * Default configuration optimized for performance
 */
export const DEFAULT_CONFIG: ImageOptimizationConfig = {
  quality: 85,
  format: 'auto',
  enablePlaceholder: true,
  enableLazyLoading: true,
  enableResponsive: true
};

/**
 * Optimized breakpoints based on real-world usage patterns
 * Updated to match actual image dimensions (600x450) and display sizes
 */
export const OPTIMIZED_BREAKPOINTS: ResponsiveImageSizes = {
  mobile: { width: 320, height: 240 },
  tablet: { width: 480, height: 360 },
  desktop: { width: 600, height: 450 },
  large: { width: 600, height: 450 } // Never exceed source image size
};

/**
 * Product-specific breakpoints optimized for grid layout
 * Based on actual grid card sizes: 280-500px wide with 4:3 aspect ratio
 */
export const PRODUCT_BREAKPOINTS: ResponsiveImageSizes = {
  mobile: { width: 300, height: 225 },   // For ~300px wide cards
  tablet: { width: 400, height: 300 },   // For ~400px wide cards
  desktop: { width: 500, height: 375 },  // For ~500px wide cards
  large: { width: 600, height: 450 }     // Maximum source size
};

/**
 * Detect if image is from Bunny CDN
 */
export function isBunnyCDNImage(url: string): boolean {
  return url.includes('b-cdn.net') || url.includes('bunnycdn.com');
}

/**
 * Detect if image is from Unsplash
 */
export function isUnsplashImage(url: string): boolean {
  return url.includes('unsplash.com') || url.includes('images.unsplash.com');
}

/**
 * Generate optimized Bunny CDN URL
 */
export function generateBunnyCDNUrl(
  baseUrl: string,
  width: number,
  height: number,
  config: Partial<ImageOptimizationConfig> = {}
): string {
  const { quality = 85, format = 'auto' } = { ...DEFAULT_CONFIG, ...config };
  
  const cleanUrl = baseUrl.split('?')[0];
  const params = new URLSearchParams({
    width: width.toString(),
    height: height.toString(),
    quality: quality.toString(),
    format: format,
    fit: 'cover',
    crop: 'smart'
  });
  
  return `${cleanUrl}?${params.toString()}`;
}

/**
 * Generate optimized Unsplash URL
 */
export function generateUnsplashUrl(
  baseUrl: string,
  width: number,
  height: number,
  config: Partial<ImageOptimizationConfig> = {}
): string {
  const { quality = 85, format = 'auto' } = { ...DEFAULT_CONFIG, ...config };
  
  const cleanUrl = baseUrl.split('?')[0];
  const params = new URLSearchParams({
    w: width.toString(),
    h: height.toString(),
    q: quality.toString(),
    auto: 'format',
    fit: 'crop',
    crop: 'smart'
  });
  
  if (format === 'webp' || format === 'auto') {
    params.set('fm', 'webp');
  }
  
  return `${cleanUrl}?${params.toString()}`;
}

/**
 * Generate responsive image URLs for different breakpoints
 */
export function generateResponsiveUrls(
  baseUrl: string,
  breakpoints: ResponsiveImageSizes = OPTIMIZED_BREAKPOINTS,
  config: Partial<ImageOptimizationConfig> = {}
): Record<string, string> {
  const urls: Record<string, string> = {};
  
  Object.entries(breakpoints).forEach(([key, { width, height }]) => {
    if (isBunnyCDNImage(baseUrl)) {
      urls[key] = generateBunnyCDNUrl(baseUrl, width, height, config);
    } else if (isUnsplashImage(baseUrl)) {
      urls[key] = generateUnsplashUrl(baseUrl, width, height, config);
    } else {
      // For other CDNs, use basic optimization
      urls[key] = baseUrl;
    }
  });
  
  return urls;
}

/**
 * Generate srcset string for responsive images
 */
export function generateSrcSet(
  baseUrl: string,
  breakpoints: ResponsiveImageSizes = OPTIMIZED_BREAKPOINTS,
  config: Partial<ImageOptimizationConfig> = {}
): string {
  const urls = generateResponsiveUrls(baseUrl, breakpoints, config);
  
  return Object.entries(breakpoints)
    .map(([key, { width }]) => `${urls[key]} ${width}w`)
    .join(', ');
}

/**
 * Generate sizes attribute for responsive images
 */
export function generateSizesAttribute(context: 'product-grid' | 'product-detail' | 'hero'): string {
  switch (context) {
    case 'product-grid':
      // Updated to match actual grid card sizes: 280-500px
      return '(max-width: 480px) 300px, (max-width: 768px) 400px, (max-width: 1200px) 500px, 500px';
    case 'product-detail':
      return '(max-width: 768px) 95vw, (max-width: 1200px) 50vw, 600px';
    case 'hero':
      return '(max-width: 768px) 100vw, (max-width: 1200px) 80vw, 1200px';
    default:
      return '(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw';
  }
}

/**
 * Generate low-quality placeholder image
 */
export function generatePlaceholder(
  baseUrl: string,
  width: number = 50,
  height: number = 38
): string {
  if (isBunnyCDNImage(baseUrl)) {
    return generateBunnyCDNUrl(baseUrl, width, height, { quality: 20, format: 'webp' });
  } else if (isUnsplashImage(baseUrl)) {
    return generateUnsplashUrl(baseUrl, width, height, { quality: 20, format: 'webp' });
  }
  
  return baseUrl;
}

/**
 * Main function to optimize any image URL
 */
export function optimizeImage(
  baseUrl: string,
  context: 'product-grid' | 'product-detail' | 'hero' = 'product-grid',
  config: Partial<ImageOptimizationConfig> = {}
): OptimizedImageResult {
  const finalConfig = { ...DEFAULT_CONFIG, ...config };
  
  // Select appropriate breakpoints based on context
  const breakpoints = context === 'product-grid' ? PRODUCT_BREAKPOINTS : OPTIMIZED_BREAKPOINTS;
  
  // Generate responsive URLs
  const responsiveUrls = generateResponsiveUrls(baseUrl, breakpoints, finalConfig);
  const srcset = generateSrcSet(baseUrl, breakpoints, finalConfig);
  const sizes = generateSizesAttribute(context);
  
  // Use desktop size as default
  const defaultSize = breakpoints.desktop;
  const src = responsiveUrls.desktop || baseUrl;
  
  // Generate placeholder if enabled
  const placeholder = finalConfig.enablePlaceholder 
    ? generatePlaceholder(baseUrl, 50, Math.round(50 * (defaultSize.height / defaultSize.width)))
    : undefined;
  
  return {
    src,
    srcset,
    sizes,
    placeholder,
    width: defaultSize.width,
    height: defaultSize.height
  };
}

/**
 * Utility to calculate optimal image dimensions based on container size
 */
export function calculateOptimalDimensions(
  containerWidth: number,
  containerHeight: number,
  devicePixelRatio: number = 1
): { width: number; height: number } {
  // Account for device pixel ratio but cap at 2x for performance
  const effectiveRatio = Math.min(devicePixelRatio, 2);
  
  return {
    width: Math.round(containerWidth * effectiveRatio),
    height: Math.round(containerHeight * effectiveRatio)
  };
}

/**
 * Check if WebP is supported (for client-side usage)
 */
export function supportsWebP(): Promise<boolean> {
  return new Promise((resolve) => {
    const webP = new Image();
    webP.onload = webP.onerror = () => {
      resolve(webP.height === 2);
    };
    webP.src = 'data:image/webp;base64,UklGRjoAAABXRUJQVlA4IC4AAACyAgCdASoCAAIALmk0mk0iIiIiIgBoSygABc6WWgAA/veff/0PP8bA//LwYAAA';
  });
}

/**
 * Check if AVIF is supported (for client-side usage)
 */
export function supportsAVIF(): Promise<boolean> {
  return new Promise((resolve) => {
    const avif = new Image();
    avif.onload = avif.onerror = () => {
      resolve(avif.height === 2);
    };
    avif.src = 'data:image/avif;base64,AAAAIGZ0eXBhdmlmAAAAAGF2aWZtaWYxbWlhZk1BMUIAAADybWV0YQAAAAAAAAAoaGRscgAAAAAAAAAAcGljdAAAAAAAAAAAAAAAAGxpYmF2aWYAAAAADnBpdG0AAAAAAAEAAAAeaWxvYwAAAABEAAABAAEAAAABAAABGgAAAB0AAAAoaWluZgAAAAAAAQAAABppbmZlAgAAAAABAABhdjAxQ29sb3IAAAAAamlwcnAAAABLaXBjbwAAABRpc3BlAAAAAAAAAAIAAAACAAAAEHBpeGkAAAAAAwgICAAAAAxhdjFDgQ0MAAAAABNjb2xybmNseAACAAIAAYAAAAAXaXBtYQAAAAAAAAABAAEEAQKDBAAAACVtZGF0EgAKCBgABogQEAwgMg8f8D///8WfhwB8+ErK42A=';
  });
}
